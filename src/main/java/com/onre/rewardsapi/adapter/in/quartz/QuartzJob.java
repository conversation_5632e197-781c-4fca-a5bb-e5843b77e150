package com.onre.rewardsapi.adapter.in.quartz;

import com.onre.rewardsapi.application.common.command.Command;
import com.onre.rewardsapi.application.common.command.CommandBus;
import lombok.extern.slf4j.Slf4j;
import org.quartz.Job;
import org.quartz.JobExecutionContext;
import org.quartz.JobExecutionException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;

/**
 * Base implementation of QuartzJob that provides common functionality.
 */
@Slf4j
public abstract class Quartz<PERSON>ob implements Job {

    @Lazy
    @Autowired
    private CommandBus commandBus;

    /**
     * Get the job type for this job implementation.
     * @return the JobType enum value
     */
    public abstract JobType getJobType();

    /**
     * Create the command to be executed by this job.
     * @param context the job execution context
     * @return the command to execute
     */
    public abstract Command<?> createCommand(JobExecutionContext context);

    /**
     * Get the job name from the job type.
     * @return the job name
     */
    public final String getJobName() {
        return getJobType().getJobName();
    }

    /**
     * Get the job group from the job type.
     * @return the job group
     */
    public final String getJobGroup() {
        return getJobType().getJobGroup();
    }

    /**
     * Get the job description from the job type.
     * @return the job description
     */
    public final String getDescription() {
        return getJobType().getDescription();
    }

    @Override
    public final void execute(JobExecutionContext context) throws JobExecutionException {
        withLogging(() -> {
            try {
                commandBus.handle(createCommand(context));
            } catch (Exception e) {
                throw new RuntimeException(e);
            }
        });
    }

    private void withLogging(Runnable block) throws JobExecutionException {
        log.info("Starting job: {}", getJobName());
        try {
            block.run();
        } catch (Exception e) {
            log.error("Error executing job: {}", getJobName(), e);
            throw new JobExecutionException(e);
        } finally {
            log.info("Finished job: {}", getJobName());
        }
    }
}
