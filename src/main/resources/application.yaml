spring:
  application:
    name: onre-rewards-api
  liquibase:
    change-log: "classpath:db/changelog/db.changelog.xml"
    default-schema: public
  datasource:
    driver-class-name: org.postgresql.Driver
    url: ${DB_URL}
    username: ${DB_USERNAME}
    password: ${DB_PASSWORD}
  jpa:
    show-sql: false
  threads:
    virtual:
      enabled: false
  quartz:
    job-store-type: jdbc
    jdbc:
      initialize-schema: never
    overwrite-existing-jobs: true
    wait-for-jobs-to-complete-on-shutdown: true
    auto-startup: true
    properties:
      org:
        quartz:
          scheduler:
            instanceName: ClusteredScheduler
            instanceId: AUTO
            idleWaitTime: 5000
            overwriteExistingjobs: true
          threadPool:
            class: org.quartz.simpl.SimpleThreadPool
            threadCount: 5
            threadPriority: 5
          jobStore:
            acquireTriggersWithinLock: true
            misfireThreshold: 60000
            class: org.quartz.impl.jdbcjobstore.JobStoreTX
            driverDelegateClass: org.quartz.impl.jdbcjobstore.PostgreSQLDelegate
            useProperties: false
            tablePrefix: QRTZ_
            isClustered: true
            clusterCheckinInterval: 60000
            dataSource: dataSource
          dataSource:
            dataSource:
              provider: hikaricp
              autoCommit: false
              class: org.quartz.utils.PoolingConnectionProvider
              driver: com.p6spy.engine.spy.P6SpyDriver
              URL: ${DB_URL}
              user: ${DB_USERNAME}
              password: ${DB_PASSWORD}
              maxConnections: 5
              validationQuery: SELECT 1
springdoc:
  api-docs:
    path: /api-docs
  swagger-ui:
    path: /api-docs/swagger-ui.html
security:
  cors:
    allow-credentials: true
    allowed-origins:
      - "*"
      - "http://localhost:3000"
      - "http://localhost:3001"
      - "http://localhost:3002"
      - "http://localhost:3003"
    allowed-methods: "*"
    allowed-headers: "*"
server:
  max-http-request-header-size: 32KB
management:
  server:
    port: 8282
  endpoints:
    web:
      exposure:
        include: '*'

sentry:
  dsn: ${SENTRY_DSN}
  exception-resolver-order: -**********
  send-default-pii: true
  max-request-body-size: always
  trace-options-requests: false